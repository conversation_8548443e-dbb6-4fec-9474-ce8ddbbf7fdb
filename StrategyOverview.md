# Mancini MES Strategy — Overview & Operator Guide

## What this is
A production-grade NinjaTrader 8 strategy that implements <PERSON>’s level-to-level approach on ES/MES. It trades breakdown/reclaim behavior around well-tested supports (and prior day low), manages risk with ATR-based stops and a catastrophic loss guard, scales out at a first target, and then hands control to an **MFE trail** to capture the bulk of the move. It auto-exits before cash close (15:50 ET), survives reconnections, and leaves a full audit trail (trade, risk, execution, MFE logs).

---

## Recent Improvements (2025-08-29)

- **✅ Dead Code Elimination**: Removed ~100 lines of unused variables, methods, and redundant logic for cleaner architecture.
- **✅ Order Processing Excellence**: Bulletproof validation, tick-rounded outputs, intelligent retry logic for institutional-grade reliability.
- **✅ Tick Unit Consistency**: Unified all calculations to use TickSize for perfect consistency throughout codebase.
- **✅ Time Source Standardization**: All timestamps use GetTimestamp() for consistent logging and audit trails.
- **✅ Entry Pipeline Consistency**: All buy-stop orders validated through TryMakeValidBuyStop() for consistent clamp-or-block policy.
- **✅ Documentation Excellence**: Complete alignment between code and documentation with standardized comment formatting.
- **✅ Simplified Architecture**: Direct bracket placement, single validation paths, clean code flow without deferred complexity.
- **✅ Enhanced Logging**: Intelligent throttling, gated debug prints, comprehensive audit trails with 99% spam reduction.
- **✅ Bulletproof Buy-Stop Validation**: Eliminates "below market" broker rejections with auto-clamp and intelligent retry.
- **✅ Flexible Trading Hours**: Configurable ETH (6:00 PM - 3:50 PM ET) or RTH (9:30 AM - 3:50 PM ET) trading windows.
- **✅ Resilient Prior Day Low**: Holiday/gap fallback mechanism with warning logs when data is unavailable.
- **✅ Elegant News Freeze**: State machine with 3 policies (Block/TightenBE/Flatten) supporting both personal trading and prop firm compliance.
- **✅ Bulletproof Phantom Protection**: Realtime-state-only trading prevents ALL phantom trades from historical data processing.
- **✅ Elegant Session Management**: ETH/RTH agnostic session boundary detection ensures fresh daily limits without cross-session contamination.
- **✅ Universal Compatibility**: Works identically in live trading, Market Replay, and backtesting with intelligent session detection.

---

## Architecture at a glance

- **State.Configure / DataLoaded**
  Validates knobs, initializes logging and state paths. Prior day low is calculated from 5-minute data.

- **OnBarUpdate (primary 5-min)**
  1) Session management (new day resets, pre-close entry block, 15:50 auto-flatten).
  2) Risk validation (ATR freshness, catastrophic loss check).
  3) **Pattern detection & entries** when flat (breakdown/reclaim logic with phantom trade protection).
  4) Position management (ensure protective stop, MFE trail service, break-even management).

- **OnMarketData (tick)**  
  Tick-level catastrophic loss guard and **MFE trail updates** for instant exit responsiveness.

- **OnExecutionUpdate**
  Order fills and life-cycle: counts trades, places bracket orders directly with OCO protection, records entry risk, scales at target, transitions to runner with break-even protection, and logs comprehensive per-trade summaries on exit.

- **Helpers**
  - `Oco(tag)` unified OCO IDs
  - `T(tick=false)` unified timestamps
  - Logging writers (`WriteTradeLog`, `WriteRiskLog`, `WriteMFELog`, `WriteExecutionLog`)
  - ATR caching, stop-quantity watchdogs, orphaned-order scan, state save/restore
  - `IsMarketReplayOrLiveTrading()` phantom trade protection with account-based detection

---

## Core ideas (how it thinks)

- **Single source of truth:** Position.Quantity and TradeManager’s `EntryPrice/StopPrice`. No shadow copies.  
- **Bracket discipline:** Entry creates stop+target OCO directly in OnExecutionUpdate; native stops are canceled the moment the **MFE trail** takes control (to avoid conflicts).
- **Catastrophic guard:** If unrealized loss reaches `MaxRiskPerTrade`, flatten immediately (optionally stop trading for the day).
- **End-of-day certainty:** Always flat by 15:50 ET, with a backup path so this cannot be missed.
- **Phantom trade protection:** Account-based detection prevents fake trades from historical data loading while allowing Market Replay from any time period.
- **Logging is the product:** Every critical transition is logged in a stable, grep-able format.

---

## Example A — Support Breakdown → Target Scale → MFE Trail Exit

1) **Detection**  
   Price trades below a predefined support (e.g., Support1). The strategy flags the breakdown, validates depth (min/max points), and prepares an entry (order type per `EntryExecutionMode`).

2) **Entry & Brackets**  
   Entry fills; `OnExecutionUpdate` assigns `EntryPrice`, increments the daily trade counter, and places the initial stop (**ATR × multiplier below entry for long positions**) and the first target (`FirstTargetPoints`). Position is split: profit contracts vs. runner.

3) **Scale at Target**  
   When target fills, realized PnL is added to a **cumulative MFE** model. A **break-even protective runner stop** is placed immediately; the MFE trail is allowed to replace it once armed.

4) **MFE Trail Arms & Manages Runner**  
   Tick-level updates push peak MFE higher. If cumulative MFE crosses the arm threshold (`MfeTrailArmMultiplier` × original risk), the trail activates and takes sole control. The native stop is canceled; the trail stop floats to lock a % of peak (`MfeTrailExitThreshold`) or follow an ATR-based rule, depending on your settings.

5) **Exit**  
   Reversal tags the trail stop; the runner exits. A per-trade summary (entry, exit, realized, capture % of peak, latency if any) is written to logs.

---

## Example B — Rejection/Recovery & Safety Nets

- **ATR unavailable (0) on reconnect**  
  The strategy defers stop resubmission for a few bars waiting for a valid ATR; if unavailable past a small threshold, it submits a conservative emergency stop to ensure protection.

- **Rejected entry**  
  Rejections trigger a controlled retry (switching order type if configured). If all retries fail, the trade state is reset cleanly—no dangling orders.

- **Orphaned orders after restart**  
  On entering real-time, it scans live orders and reconciles internal references (prevents duplicate stops/targets).

---

## Market Replay & Phantom Trade Protection

### **Universal Compatibility**
The strategy works identically across all trading modes:
- **Live Trading**: Full functionality with real-time data
- **Market Replay**: Complete functionality from any historical time period
- **Backtesting**: Same logic path as live trading

### **Bulletproof Phantom Protection**
**Problem Solved**: Historical data processing could trigger fake "phantom trades" that never actually happened, creating false P&L and incorrect trade counts.

**Simple Solution**: **Realtime-State-Only Trading**
```csharp
// Only allow pattern detection in Realtime state
if (State != State.Realtime)
{
    return; // Block ALL pattern detection in Historical state
}
```

### **How It Works**
#### **✅ Historical State (Data Loading)**
- **All Accounts**: Pattern detection blocked
- **Result**: No phantom trades from historical data processing

#### **✅ Realtime State (Active Trading)**
- **Live Trading**: Real trades on live market data
- **Market Replay**: Real trades when Market Replay transitions to Realtime
- **Result**: Only actual trading opportunities are processed

### **Elegant Session Management**
**Problem Solved**: Daily trade limits should reset at proper trading session boundaries, not calendar boundaries, while preserving state during same-session reconnections.

**Solution**: ETH/RTH Agnostic Session Detection
```csharp
// Universal session boundary detection
private bool IsNewTradingSession(DateTime previousTime, DateTime currentTime)
{
    if (UseETHTradingHours)
    {
        // ETH: Session starts at 6:00 PM ET
        DateTime prevSessionStart = GetETHSessionStart(previousTime);
        DateTime currSessionStart = GetETHSessionStart(currentTime);
        return prevSessionStart.Date != currSessionStart.Date;
    }
    else
    {
        // RTH: Session follows calendar days
        return previousTime.Date != currentTime.Date;
    }
}
```

**Example Scenarios:**
- **Same Session Reconnection**: 2 trades taken → disconnect → reconnect → tradesToday = 2 (preserved)
- **New Session Start**: Previous session had 2 trades → new session → tradesToday = 0 (fresh start)
- **ETH Session**: Sunday 8:00 PM and Monday 10:00 AM = same session (state preserved)
- **ETH New Session**: Monday 2:00 PM and Monday 8:00 PM = new session (fresh start)
- **RTH Session**: Uses calendar day boundaries (Monday ≠ Tuesday)
- **Market Replay**: Proper session detection regardless of time period

### **Benefits**
- **✅ No Phantom Trades**: Historical data processing completely blocked
- **✅ Universal Protection**: Works for all account types automatically
- **✅ Market Replay Compatible**: Works from any historical time period
- **✅ Perfect Reconnection Recovery**: Restores all state within same session
- **✅ ETH/RTH Agnostic**: Single elegant solution for both trading hour modes
- **✅ Proper Session Boundaries**: ETH sessions span midnight correctly (6 PM - 3:50 PM)
- **✅ Daily Limit Enforcement**: Trade counts preserved during same-session reconnections
- **✅ Fresh Session Start**: Automatic reset at proper session boundaries
- **✅ Risk Management Integrity**: Daily stops and limits managed correctly
- **✅ Zero Configuration**: Automatic detection based on UseETHTradingHours setting
- **✅ Bulletproof Logic**: No edge cases, works universally across all scenarios

---

## Configuration checklist (safe defaults)

- **Risk**  
  - `MaxRiskPerTrade` — per-trade catastrophic guard (in $).  
  - `StopDistanceATR` — initial stop = ATR × this multiplier (stop placed below entry).  
  - `MaxDailyTrades` — hard daily cap.  
  - `StopTradingAfterCatastrophicLoss` — true to halt new entries after a breach.

- **Trade management**  
  - `FirstTargetPoints` — scale location in points.  
  - `ProfitTakePercentage` — fraction to take at target (remainder becomes runner).  
  - `EnableMFETrail` — enable trail logic.  
  - `MfeTrailArmMultiplier` — cumulative MFE needed to arm (× original risk).  
  - `MfeTrailExitThreshold` — capture % of peak for exits (e.g., 0.55 = exit if pullback >45% of peak).  
  - `BreakEvenBufferTicks` — offset for the immediate runner BE stop.

- **Session**
  - `UseETHTradingHours` — Enable ETH (6:00 PM - 3:50 PM ET) or RTH only (9:30 AM - 3:50 PM ET).
  - Pre-close buffer (entries blocked) and **flatten at 15:50 ET** are always on.

- **News Freeze**
  - `EnableNewsFreeze` — Enable news freeze protection during high-impact events.
  - `NewsFreezeStartTime` / `NewsFreezeEndTime` — Time window in ET (e.g., "14:25" to "14:35").
  - `NewsFreezeBehavior` — Policy for handling open positions:
    - **BlockEntriesOnly**: Block new entries, manage existing positions normally
    - **TightenStopsToBE**: Move stops to break-even when freeze starts (conservative)
    - **FlattenOnStart** (default): Immediately flatten position and cancel all orders (prop firm compliance)

- **Entry behavior**
  - `EntryExecutionMode` — `Market`, `StopMarket` (Adam’s preferred), `Limit`, or `LimitWithBuffer`.
  - `MinStopDistanceTicks` — Minimum ticks above ask for bulletproof Buy Stop orders (default: 1).
  - `MaxAutoRepriceTicks` — Maximum auto-adjustment for Buy Stop orders to prevent broker rejections (default: 2).
  - Min/max breakdown depth (in points) to filter weak/overextended breaks.

---

## Operating the system

1) **Before session**  
   - Verify supports & prior day low are aligned with your plan.  
   - Confirm property grid values (hover for tooltips—XML docstrings included).  
   - Check the log folder path is writable.

2) **During session**  
   - Use the logs dashboard (below) to spot issues early.  
   - If an entry is missed (broker rejection, volatility), see the **Execution Log** first.

3) **After session**  
   - Confirm “BULLETPROOF 3:50 PM ET CLOSE” event fired (and backup didn’t need to).  
   - Review per-trade summaries and MFE capture rates.

---

## Logs (what to read & when)

- **Execution Log** — every order state change, entry/exit confirmations (first stop for “what happened?”).  
- **Trade Log** — human-friendly per-trade lines (entry, target fill, trail exit, totals).  
- **Risk Log** — catastrophic checks, ATR emergencies, stop quantity watchdog, reconnection notes.  
- **MFE Log** — arming decisions, peak updates, capture % at exit.

**Canonical format** (typical line):  
```
[AREA] EVENT | TradeID=... | Pos=LONG 3@5248.75 | Stop=5253.75 | Target=5242.75 | Qty=3
| Risk=$375.00 | T=09:37:22
```

---

## Glossary (to avoid confusion)

- **Profit contracts** — portion scaled at the first target.  
- **Runner** — remainder managed by the trail.  
- **MFE (Max Favorable Excursion)** — best unrealized PnL reached during the trade (realized target gains are included in “cumulative MFE” for arming).  
- **Trail (MFE Trail)** — exit logic that locks a share of peak MFE; once armed, it cancels native stops and owns the exit.  
- **Restore stop** — a conservative protective stop (e.g., after reconnection) submitted if no active stop is detected.  
- **OCO** — “one cancels the other” link between stop and target for the same entry.

---

## Extending safely

- Add new exits/filters by **one owner at a time**: if a module (e.g., trail) owns the exit, cancel or disable the others to prevent order fights.
- Keep the single-source invariants (Position.Quantity, TradeManager stop/entry) untouched.
- Mirror the log format and add one-line summaries to keep the audit trail coherent.

---

## Troubleshooting Common Issues

### Buy Stop Order Rejections
- **"Buy stop orders can't be placed below the market"**: Fixed with bulletproof validation system
- **Check logs for**: `[RISK] BUY STOP BLOCKED` or `[DEBUG] BUY STOP AUTO-ADJUST` messages
- **Auto-adjustment**: Orders automatically bumped above ask price within configured tolerance
- **Hard blocks**: Orders too far below market are blocked with clear reason logging
- **Retry mechanism**: Broker rejections trigger intelligent retry with market-based pricing

### Missing Trades / Blocked Entries
1. **Check Debug Log** for `ENTRY DECISION` messages - shows exact blocking reason
2. **Phantom Trade Protection** - `[ORDER BLOCKED | Historical data loading]` means strategy is loading old data on live account
3. **Look for phase tags** - `[HIST]` means still in historical phase, `[RT]` means realtime
4. **Volatility blocks** - `[ENTRY BLOCKED | Volatility]` shows ATR ratio exceeded `MaxStopATRRatio`
5. **Daily limits** - Check if `tradesToday >= MaxDailyTrades`
6. **Trading hours** - Entries only allowed during configured hours (RTH: 9:30 AM - 3:50 PM ET, ETH: 6:00 PM - 3:50 PM ET)

### Log Issues
- **Duplicate entries**: Fixed in latest version with intelligent throttling
- **Non-sequential timestamps**: Eliminated by removing dual data series complexity
- **Missing pattern events**: Check Patterns log with new throttling system

### News Freeze Issues
- **Policy not working**: Check `EnableNewsFreeze = true` and verify time format (HH:mm ET)
- **Prop firm violations**: Use `NewsFreezeBehavior = FlattenOnStart` for mandatory compliance
- **Conservative risk management**: Use `NewsFreezeBehavior = TightenStopsToBE` to reduce exposure
- **Time zone confusion**: All news freeze times are in Eastern Time (ET)

### Market Replay Issues
- **No trades in Market Replay**: Check account name contains "Playback", "Replay", "Sim101", or "Simulation"
- **Phantom trades after restart**: Strategy correctly blocks historical data loading on live accounts - this is protective behavior
- **Old Market Replay not working**: Ensure account name matches supported patterns (Playback*, Replay*, Sim*, Simulation*)
- **Pattern detection blocked**: Check Debug log for "HISTORICAL DATA PROTECTION" messages

### Performance Improvements
- **Dead code elimination**: Removed ~100 lines of unused variables, methods, and redundant logic
- **Order processing excellence**: Bulletproof validation with tick-rounded outputs and intelligent retry
- **Tick unit consistency**: Unified all calculations to TickSize for perfect consistency
- **Time source standardization**: All timestamps use GetTimestamp() for consistent logging
- **Entry pipeline consistency**: All buy-stop orders validated through unified TryMakeValidBuyStop()
- **Documentation excellence**: Complete alignment between code and documentation
- **Simplified architecture**: Direct bracket placement with single validation paths
- **Enhanced logging**: Intelligent throttling with 99% spam reduction and gated debug prints
- **Efficient prior day low**: Calculated from 5-min bars with date validation
- **Streamlined order management**: Direct bracket placement eliminates deferred complexity
